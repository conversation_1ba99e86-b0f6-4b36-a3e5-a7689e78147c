// 相册功能JavaScript

// 全局变量
let currentAlbums = [];
let currentAlbumId = null;
let currentAlbumMedia = [];
let currentMediaIndex = 0;
let editingAlbumId = null;
let deletingAlbumId = null;
let deletingMediaId = null;
let coverImageData = null;

// 确保封面上传事件监听器正确绑定的通用函数
function ensureCoverUploadListener() {
    const coverUpload = document.getElementById('cover-upload');
    if (coverUpload) {
        // 移除可能存在的旧监听器
        coverUpload.removeEventListener('change', handleCoverUpload);
        // 添加新监听器
        coverUpload.addEventListener('change', handleCoverUpload);

        return true;
    }
    console.log('⚠️ 找不到封面上传元素');
    return false;
}

// 确保位置控制按钮事件监听器正确绑定的通用函数
function ensurePositionButtonListeners() {
    const positionBtns = document.querySelectorAll('.position-btn');
    positionBtns.forEach(btn => {
        // 移除可能存在的旧监听器
        btn.removeEventListener('click', handlePositionChange);
        // 添加新监听器
        btn.addEventListener('click', handlePositionChange);
    });
    if (positionBtns.length > 0) {
        console.log('🔧 位置控制按钮事件监听器已确保绑定');
        return true;
    }
    console.log('⚠️ 找不到位置控制按钮');
    return false;
}

// 清理封面预览和上传区域的事件监听器
function clearCoverPreviewListeners() {
    const coverPreview = document.getElementById('cover-preview');
    if (coverPreview) {
        // 保存当前内容
        const currentHTML = coverPreview.innerHTML;
        // 创建一个新的元素来替换旧的，这样可以清除所有事件监听器
        const newCoverPreview = coverPreview.cloneNode(false);
        // 恢复内容
        newCoverPreview.innerHTML = currentHTML;
        coverPreview.parentNode.replaceChild(newCoverPreview, coverPreview);
        console.log('🧹 封面预览事件监听器已清理');
    }

    // 清理上传区域的事件监听器
    const coverUploadArea = document.querySelector('.cover-upload-area');
    if (coverUploadArea) {
        const newCoverUploadArea = coverUploadArea.cloneNode(true);
        coverUploadArea.parentNode.replaceChild(newCoverUploadArea, coverUploadArea);
        console.log('🧹 封面上传区域事件监听器已清理');
    }

    return null;
}

// 初始化相册页面
function initAlbums() {
    loadAlbums();
    setupAlbumEventListeners();
}

// 初始化相册详情页面
function initAlbumDetail() {
    console.log('initAlbumDetail 被调用');

    const urlParams = new URLSearchParams(window.location.search);
    currentAlbumId = urlParams.get('albumId');

    console.log('从URL获取的相册ID:', currentAlbumId);

    // 验证相册ID的有效性
    if (currentAlbumId && currentAlbumId.trim() !== '' &&
        currentAlbumId !== 'null' && currentAlbumId !== 'undefined') {

        const isValidId = /^[a-zA-Z0-9_-]+$/.test(currentAlbumId.trim());
        if (isValidId) {
            // 等待页面DOM完全加载后再初始化
            setTimeout(() => {
                loadAlbumDetail(currentAlbumId);
                setupAlbumDetailEventListeners();
            }, 100);
        } else {
            console.log('相册ID格式无效，跳转到相册列表页面');
            // 清理无效的URL参数
            window.history.replaceState({}, document.title, window.location.pathname);
            setTimeout(() => {
                showPage('albums');
            }, 100);
        }
    } else {
        console.log('相册ID无效，跳转到相册列表页面');
        // 清理URL参数
        if (window.location.search) {
            window.history.replaceState({}, document.title, window.location.pathname);
        }
        setTimeout(() => {
            showPage('albums');
        }, 100);
    }
}

// 设置相册页面事件监听器
function setupAlbumEventListeners() {
    // 封面上传事件监听器在弹窗显示时绑定，这里不重复绑定


    // 封面位置调整按钮
    const positionBtns = document.querySelectorAll('.position-btn');
    positionBtns.forEach(btn => {
        btn.removeEventListener('click', handlePositionChange);
        btn.addEventListener('click', handlePositionChange);
    });
    if (positionBtns.length > 0) {

    }

    // 相册表单提交
    const albumForm = document.getElementById('album-form');
    if (albumForm) {
        albumForm.removeEventListener('submit', handleAlbumSubmit);
        albumForm.addEventListener('submit', handleAlbumSubmit);

    }
}

// 设置相册详情页面事件监听器
function setupAlbumDetailEventListeners() {
    console.log('setupAlbumDetailEventListeners 被调用');

    // 等待DOM元素完全加载
    setTimeout(() => {
        // 编辑相册按钮
        const editBtn = document.getElementById('edit-album-btn');
        if (editBtn) {
            // 移除之前的事件监听器
            editBtn.replaceWith(editBtn.cloneNode(true));
            const newEditBtn = document.getElementById('edit-album-btn');
            newEditBtn.addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();
                console.log('编辑按钮被点击');
                editCurrentAlbum();
            });
            console.log('编辑按钮事件监听器已绑定');
        } else {
            console.log('编辑按钮未找到');
        }

        // 删除相册按钮
        const deleteBtn = document.getElementById('delete-album-btn');
        if (deleteBtn) {
            // 移除之前的事件监听器
            deleteBtn.replaceWith(deleteBtn.cloneNode(true));
            const newDeleteBtn = document.getElementById('delete-album-btn');
            newDeleteBtn.addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();
                console.log('删除按钮被点击');
                deleteCurrentAlbum();
            });
            console.log('删除按钮事件监听器已绑定');
        } else {
            console.log('删除按钮未找到');
        }

        // 媒体上传
        const mediaUpload = document.getElementById('media-upload');
        if (mediaUpload) {
            mediaUpload.replaceWith(mediaUpload.cloneNode(true));
            const newMediaUpload = document.getElementById('media-upload');
            newMediaUpload.addEventListener('change', handleAlbumMediaUpload);
            console.log('媒体上传事件监听器已绑定');
        }

        // 拖拽上传
        const uploadArea = document.getElementById('upload-area');
        if (uploadArea) {
            uploadArea.addEventListener('dragover', handleDragOver);
            uploadArea.addEventListener('dragleave', handleDragLeave);
            uploadArea.addEventListener('drop', handleDrop);
            console.log('拖拽上传事件监听器已绑定');
        }

        // 封面上传事件监听器在弹窗显示时绑定，这里不重复绑定
        console.log('封面上传事件监听器将在弹窗显示时绑定');

        // 重新绑定封面位置调整按钮
        const positionBtns = document.querySelectorAll('.position-btn');
        positionBtns.forEach(btn => {
            btn.replaceWith(btn.cloneNode(true));
        });
        const newPositionBtns = document.querySelectorAll('.position-btn');
        newPositionBtns.forEach(btn => {
            btn.addEventListener('click', handlePositionChange);
        });
        console.log('封面位置调整按钮事件监听器已绑定');

        // 相册表单提交（编辑相册时需要）
        const albumForm = document.getElementById('album-form');
        if (albumForm) {
            albumForm.replaceWith(albumForm.cloneNode(true));
            const newAlbumForm = document.getElementById('album-form');
            newAlbumForm.addEventListener('submit', handleAlbumSubmit);

        }

        // 验证关键弹窗元素是否存在
        setTimeout(() => {
            const albumModal = document.getElementById('album-modal');
            const deleteModal = document.getElementById('delete-confirm-modal');
            console.log('弹窗元素检查:', {
                albumModal: !!albumModal,
                deleteModal: !!deleteModal
            });
        }, 200);
    }, 300); // 增加等待时间确保DOM完全加载
}

// 加载用户相册列表
async function loadAlbums() {
    try {
        const response = await fetch('/api/albums', {
            method: 'GET',
            credentials: 'include'
        });
        
        if (response.ok) {
            const data = await response.json();
            currentAlbums = data.albums || [];
            renderAlbums();
        } else {
            throw new Error('加载相册失败');
        }
    } catch (error) {
        console.error('加载相册错误:', error);
        showToast('加载相册失败', 'error');
        renderAlbums(); // 显示空状态
    }
}

// 渲染相册列表
function renderAlbums() {
    const albumsGrid = document.getElementById('albums-grid');
    const emptyState = document.getElementById('empty-albums');
    
    if (!albumsGrid || !emptyState) return;
    
    if (currentAlbums.length === 0) {
        albumsGrid.style.display = 'none';
        emptyState.style.display = 'block';
        return;
    }
    
    albumsGrid.style.display = 'grid';
    emptyState.style.display = 'none';
    
    albumsGrid.innerHTML = currentAlbums.map(album => {
        const coverPosition = album.cover_position || 'top';
        let backgroundPosition;
        switch(coverPosition) {
            case 'top':
                backgroundPosition = 'top center';
                break;
            case 'center':
                backgroundPosition = 'center';
                break;
            case 'bottom':
                backgroundPosition = 'bottom center';
                break;
            default:
                backgroundPosition = 'top center';
        }

        return `
        <div class="album-card" onclick="openAlbumDetail(${album.id})">
            <div class="album-cover" style="${album.cover_image ? `background-image: url(${album.cover_image}); background-position: ${backgroundPosition};` : ''}">
                ${!album.cover_image ? '<i class="fas fa-images"></i>' : ''}
            </div>
            <div class="album-info">
                <div class="album-name">${escapeHtml(album.name)}</div>
                <div class="album-stats">
                    <span>${album.media_count || 0} 张照片</span>
                    <span>${formatDate(album.created_at)}</span>
                </div>
                ${album.description ? `<div class="album-description">${escapeHtml(album.description)}</div>` : ''}
            </div>
        </div>
        `;
    }).join('');
}

// 显示创建相册弹窗
function showCreateAlbumModal() {
    editingAlbumId = null;
    coverImageData = null;
    window.selectedCoverPosition = 'top'; // 默认位置

    // 清理封面预览的旧事件监听器
    clearCoverPreviewListeners();

    const modal = document.getElementById('album-modal');
    const title = document.getElementById('album-modal-title');
    const nameInput = document.getElementById('album-name');
    const descInput = document.getElementById('album-description');
    const newCoverPreview = document.getElementById('cover-preview');
    const saveBtn = document.getElementById('save-album-btn');
    const positionControl = document.getElementById('cover-position-control');

    if (modal && title && nameInput && descInput && newCoverPreview && saveBtn) {
        title.textContent = '创建相册';
        nameInput.value = '';
        descInput.value = '';
        newCoverPreview.innerHTML = '<i class="fas fa-camera"></i><span>点击选择封面图片</span>';

        // 为封面上传区域添加点击事件监听器
        const coverUploadArea = document.querySelector('.cover-upload-area');
        if (coverUploadArea) {
            coverUploadArea.addEventListener('click', function() {
                console.log('🖱️ 创建相册封面上传区域被点击，触发文件选择');
                document.getElementById('cover-upload').click();
            });
        }

        saveBtn.querySelector('.btn-text').textContent = '创建';

        // 隐藏位置控件并重置为默认状态
        if (positionControl) {
            positionControl.style.display = 'none';
            document.querySelectorAll('.position-btn').forEach(btn => {
                btn.classList.remove('active');
                if (btn.dataset.position === 'top') {
                    btn.classList.add('active');
                }
            });
        }

        modal.style.display = 'flex';
        setTimeout(() => {
            modal.classList.add('active');
            // 确保封面上传事件监听器正确绑定
            ensureCoverUploadListener();
            // 重新绑定位置控制按钮的事件监听器
            ensurePositionButtonListeners();
        }, 10);
    }
}

// 编辑当前相册
async function editCurrentAlbum() {
    console.log('=== editCurrentAlbum 被调用 ===');
    console.log('currentAlbumId:', currentAlbumId);

    if (!currentAlbumId) {
        console.log('currentAlbumId 为空，退出');
        showToast('相册ID无效', 'error');
        return;
    }

    // 先尝试从currentAlbums中查找
    let album = currentAlbums.find(a => a.id == currentAlbumId);
    console.log('从 currentAlbums 中查找到的相册:', album);

    // 如果没有找到，从服务器获取相册信息
    if (!album) {
        console.log('从服务器获取相册信息...');
        try {
            const response = await fetch(`/api/albums/${currentAlbumId}`, {
                method: 'GET',
                credentials: 'include'
            });

            if (response.ok) {
                const data = await response.json();
                album = data.album;
                console.log('从服务器获取到的相册信息:', album);
            } else {
                console.log('获取相册信息失败，响应状态:', response.status);
                showToast('获取相册信息失败', 'error');
                return;
            }
        } catch (error) {
            console.error('获取相册信息错误:', error);
            showToast('获取相册信息失败', 'error');
            return;
        }
    }

    if (!album) {
        console.log('最终没有获取到相册信息');
        showToast('相册信息获取失败', 'error');
        return;
    }

    // 弹窗元素现在在主页面中，应该总是可用
    const modal = document.getElementById('album-modal');
    const title = document.getElementById('album-modal-title');
    const nameInput = document.getElementById('album-name');
    const descInput = document.getElementById('album-description');
    const coverPreview = document.getElementById('cover-preview');
    const saveBtn = document.getElementById('save-album-btn');

    console.log('弹窗元素检查:', {
        modal: !!modal,
        title: !!title,
        nameInput: !!nameInput,
        descInput: !!descInput,
        coverPreview: !!coverPreview,
        saveBtn: !!saveBtn
    });

    if (!modal || !title || !nameInput || !descInput || !coverPreview || !saveBtn) {
        console.error('弹窗元素不完整，无法显示编辑界面');
        showToast('编辑界面加载失败', 'error');
        return;
    }

    editingAlbumId = currentAlbumId;
    coverImageData = null;

    // 清理封面预览的旧事件监听器
    clearCoverPreviewListeners();

    // 重新获取清理后的封面预览元素
    const newCoverPreview = document.getElementById('cover-preview');

    console.log('设置弹窗内容...');
    title.textContent = '编辑相册';
    nameInput.value = album.name;
    descInput.value = album.description || '';

    const positionControl = document.getElementById('cover-position-control');

    if (album.cover_image) {
        // 设置当前位置
        const currentPosition = album.cover_position || 'top';
        let objectPosition;
        switch(currentPosition) {
            case 'top':
                objectPosition = 'top center';
                break;
            case 'center':
                objectPosition = 'center';
                break;
            case 'bottom':
                objectPosition = 'bottom center';
                break;
            default:
                objectPosition = 'top center';
        }

        newCoverPreview.innerHTML = `<img src="${album.cover_image}" alt="封面预览" style="object-position: ${objectPosition}; cursor: pointer;">`;

        // 显示位置控件并设置当前位置
        if (positionControl) {
            positionControl.style.display = 'block';
            window.selectedCoverPosition = currentPosition;

            // 更新按钮状态
            document.querySelectorAll('.position-btn').forEach(btn => {
                btn.classList.remove('active');
                if (btn.dataset.position === currentPosition) {
                    btn.classList.add('active');
                }
            });
        }
    } else {
        newCoverPreview.innerHTML = '<i class="fas fa-camera"></i><span>点击选择封面图片</span>';

        // 隐藏位置控件
        if (positionControl) {
            positionControl.style.display = 'none';
        }
    }

    // 为封面上传区域添加点击事件监听器（统一处理有无封面的情况）
    const coverUploadArea = document.querySelector('.cover-upload-area');
    if (coverUploadArea) {
        coverUploadArea.addEventListener('click', function() {
            console.log('🖱️ 编辑相册封面上传区域被点击，触发文件选择');
            document.getElementById('cover-upload').click();
        });
    }

    const btnText = saveBtn.querySelector('.btn-text');
    if (btnText) {
        btnText.textContent = '保存';
    }

    console.log('显示弹窗...');
    modal.style.display = 'flex';
    // 强制重排
    modal.offsetHeight;
    setTimeout(() => {
        modal.classList.add('active');
        // 确保封面上传事件监听器正确绑定
        ensureCoverUploadListener();
        // 重新绑定位置控制按钮的事件监听器
        ensurePositionButtonListeners();
        console.log('✅ 编辑弹窗显示成功');
    }, 10);
}

// 隐藏相册弹窗
function hideAlbumModal() {
    const modal = document.getElementById('album-modal');
    if (modal) {
        modal.classList.remove('active');
        setTimeout(() => {
            modal.style.display = 'none';
        }, 300);
    }
}

// 处理封面上传
function handleCoverUpload(event) {
    console.log('🎯 handleCoverUpload 被调用');
    const file = event.target.files[0];
    if (!file) {
        console.log('❌ 没有选择文件');
        return;
    }

    console.log('📁 选择的文件:', file.name, file.type, file.size);

    // 验证文件类型
    if (!file.type.startsWith('image/')) {
        console.log('❌ 文件类型错误:', file.type);
        showToast('请选择图片文件', 'error');
        return;
    }

    // 验证文件大小 (20MB)
    if (file.size > 20 * 1024 * 1024) {
        console.log('❌ 文件过大:', file.size);
        showToast('图片大小不能超过20MB', 'error');
        return;
    }

    console.log('✅ 文件验证通过，开始读取...');
    const reader = new FileReader();
    reader.onload = function(e) {
        console.log('✅ 文件读取完成，更新预览...');
        coverImageData = e.target.result;
        const coverPreview = document.getElementById('cover-preview');
        const positionControl = document.getElementById('cover-position-control');

        if (coverPreview) {
            // 获取当前选择的位置，如果没有选择则默认为"上部"
            const currentPosition = window.selectedCoverPosition || 'top';
            let objectPosition;
            switch(currentPosition) {
                case 'top':
                    objectPosition = 'top center';
                    break;
                case 'center':
                    objectPosition = 'center';
                    break;
                case 'bottom':
                    objectPosition = 'bottom center';
                    break;
                default:
                    objectPosition = 'top center';
            }

            coverPreview.innerHTML = `<img src="${e.target.result}" alt="封面预览" style="object-position: ${objectPosition}; cursor: pointer;">`;

            console.log('✅ 封面预览已更新，位置:', currentPosition);
        } else {
            console.log('❌ 找不到封面预览元素');
        }

        // 显示位置调整控件，保持当前选择的位置
        if (positionControl) {
            positionControl.style.display = 'block';

            // 保持当前选择的位置，如果没有选择则默认为"上部"
            const currentPosition = window.selectedCoverPosition || 'top';
            window.selectedCoverPosition = currentPosition;

            // 更新图片的显示位置
            const coverImg = coverPreview.querySelector('img');
            if (coverImg) {
                let objectPosition;
                switch(currentPosition) {
                    case 'top':
                        objectPosition = 'top center';
                        break;
                    case 'center':
                        objectPosition = 'center';
                        break;
                    case 'bottom':
                        objectPosition = 'bottom center';
                        break;
                    default:
                        objectPosition = 'top center';
                }
                coverImg.style.objectPosition = objectPosition;
            }

            // 更新按钮状态
            document.querySelectorAll('.position-btn').forEach(btn => {
                btn.classList.remove('active');
                if (btn.dataset.position === currentPosition) {
                    btn.classList.add('active');
                }
            });
            console.log('✅ 位置控制按钮已显示，当前位置:', currentPosition);
        } else {
            console.log('⚠️ 找不到位置控制元素');
        }
    };

    reader.onerror = function(error) {
        console.log('❌ 文件读取失败:', error);
        showToast('图片读取失败', 'error');
    };

    reader.readAsDataURL(file);
}

// 处理封面位置调整
function handlePositionChange(event) {
    const position = event.target.dataset.position;
    const coverPreview = document.getElementById('cover-preview');
    const img = coverPreview?.querySelector('img');

    if (!img) return;

    // 更新按钮状态
    document.querySelectorAll('.position-btn').forEach(btn => {
        btn.classList.remove('active');
    });
    event.target.classList.add('active');

    // 更新预览图片的显示位置
    let objectPosition;
    switch(position) {
        case 'top':
            objectPosition = 'top center';
            break;
        case 'center':
            objectPosition = 'center';
            break;
        case 'bottom':
            objectPosition = 'bottom center';
            break;
        default:
            objectPosition = 'top center';
    }

    // 将位置信息存储到全局变量中
    window.selectedCoverPosition = position;

    // 更新预览图片的显示位置
    img.style.objectPosition = objectPosition;

    console.log('🎯 封面位置已更新为:', position, '显示位置:', objectPosition);
}

// 处理相册表单提交
async function handleAlbumSubmit(event) {
    event.preventDefault();
    
    const nameInput = document.getElementById('album-name');
    const descInput = document.getElementById('album-description');
    const saveBtn = document.getElementById('save-album-btn');
    
    if (!nameInput || !descInput || !saveBtn) return;
    
    const name = nameInput.value.trim();
    if (!name) {
        showToast('请输入相册名称', 'error');
        return;
    }
    
    // 显示加载状态
    saveBtn.classList.add('loading');
    saveBtn.disabled = true;
    
    try {
        const albumData = {
            name: name,
            description: descInput.value.trim(),
            cover_image: coverImageData,
            cover_position: window.selectedCoverPosition || 'top'
        };
        
        const url = editingAlbumId ? `/api/albums/${editingAlbumId}` : '/api/albums';
        const method = editingAlbumId ? 'PUT' : 'POST';
        
        const response = await fetch(url, {
            method: method,
            headers: {
                'Content-Type': 'application/json'
            },
            credentials: 'include',
            body: JSON.stringify(albumData)
        });
        
        if (response.ok) {
            const result = await response.json();
            showToast(editingAlbumId ? '相册更新成功' : '相册创建成功', 'success');
            hideAlbumModal();
            
            if (editingAlbumId) {
                // 如果是编辑模式，重新加载相册详情
                loadAlbumDetail(editingAlbumId);
            } else {
                // 如果是创建模式，重新加载相册列表
                loadAlbums();
            }
        } else {
            const error = await response.json();
            throw new Error(error.message || '操作失败');
        }
    } catch (error) {
        console.error('相册操作错误:', error);
        showToast(error.message || '操作失败', 'error');
    } finally {
        saveBtn.classList.remove('loading');
        saveBtn.disabled = false;
    }
}

// 打开相册详情
function openAlbumDetail(albumId) {
    // 使用URL参数传递相册ID，然后切换页面
    window.history.pushState({}, '', `?albumId=${albumId}`);
    showPage('album-detail');
}

// 格式化日期
function formatDate(dateString) {
    const date = new Date(dateString);
    const now = new Date();
    const diffTime = Math.abs(now - date);
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    
    if (diffDays === 1) {
        return '昨天';
    } else if (diffDays < 7) {
        return `${diffDays}天前`;
    } else if (diffDays < 30) {
        return `${Math.ceil(diffDays / 7)}周前`;
    } else {
        return date.toLocaleDateString('zh-CN');
    }
}

// 加载相册详情
async function loadAlbumDetail(albumId) {
    try {
        // 加载相册信息
        const albumResponse = await fetch(`/api/albums/${albumId}`, {
            method: 'GET',
            credentials: 'include'
        });

        if (!albumResponse.ok) {
            // 如果是404错误，说明相册不存在，静默跳转
            if (albumResponse.status === 404) {
                console.log('相册不存在，静默跳转到聊天页面');
                // 清理URL参数
                window.history.replaceState({}, document.title, window.location.pathname);
                setTimeout(() => {
                    showPage('chat');
                }, 100);
                return;
            }
            throw new Error('加载相册信息失败');
        }

        const albumData = await albumResponse.json();
        const album = albumData.album;

        // 更新页面标题和信息
        const albumTitle = document.getElementById('album-title');
        const mediaCount = document.getElementById('media-count');
        const albumDate = document.getElementById('album-date');
        const albumDesc = document.getElementById('album-description-text');

        if (albumTitle) albumTitle.textContent = album.name;
        if (albumDate) albumDate.textContent = `创建于 ${formatDate(album.created_at)}`;
        if (albumDesc) {
            albumDesc.textContent = album.description || '';
            albumDesc.style.display = album.description ? 'block' : 'none';
        }

        // 加载媒体文件
        const mediaResponse = await fetch(`/api/albums/${albumId}/media`, {
            method: 'GET',
            credentials: 'include'
        });

        if (mediaResponse.ok) {
            const mediaData = await mediaResponse.json();
            currentAlbumMedia = mediaData.media || [];

            if (mediaCount) {
                const count = currentAlbumMedia.length;
                mediaCount.textContent = count === 0 ? '空相册' :
                    count === 1 ? '1 张照片' : `${count} 张照片`;
            }

            renderAlbumMedia();
        } else {
            throw new Error('加载媒体文件失败');
        }

    } catch (error) {
        console.error('加载相册详情错误:', error);

        // 静默处理所有错误，直接跳转到聊天页面
        console.log('相册加载失败，静默跳转到聊天页面');
        // 清理URL参数
        window.history.replaceState({}, document.title, window.location.pathname);
        setTimeout(() => {
            showPage('chat');
        }, 100);
    }
}

// 渲染相册媒体
function renderAlbumMedia() {
    const mediaGrid = document.getElementById('media-grid');
    const emptyMedia = document.getElementById('empty-media');

    if (!mediaGrid || !emptyMedia) return;

    if (currentAlbumMedia.length === 0) {
        mediaGrid.style.display = 'none';
        emptyMedia.style.display = 'block';
        return;
    }

    mediaGrid.style.display = 'grid';
    emptyMedia.style.display = 'none';

    mediaGrid.innerHTML = currentAlbumMedia.map((media, index) => `
        <div class="media-item">
            <div class="media-content" onclick="showAlbumMediaPreview(${index})">
                ${media.media_type === 'video' ?
                    `<video src="${media.media_url}" preload="metadata"></video>
                     <div class="video-overlay"><i class="fas fa-play"></i></div>` :
                    `<img src="${media.media_url}" alt="照片">`
                }
            </div>
            <div class="media-actions">
                <button class="delete-media-btn" onclick="deleteMediaById(${media.id})" title="删除">
                    <i class="fas fa-trash"></i>
                </button>
            </div>
        </div>
    `).join('');
}

// 处理相册媒体上传
async function handleAlbumMediaUpload(event) {
    const files = Array.from(event.target.files);
    if (files.length === 0) return;

    await uploadMediaFiles(files);
}

// 处理拖拽上传
function handleDragOver(event) {
    event.preventDefault();
    event.currentTarget.classList.add('dragover');
}

function handleDragLeave(event) {
    event.preventDefault();
    event.currentTarget.classList.remove('dragover');
}

function handleDrop(event) {
    event.preventDefault();
    event.currentTarget.classList.remove('dragover');

    const files = Array.from(event.dataTransfer.files);
    if (files.length === 0) return;

    uploadMediaFiles(files);
}

// 上传媒体文件
async function uploadMediaFiles(files) {
    // 检查当前相册ID是否有效
    if (!currentAlbumId || currentAlbumId === 'null' || currentAlbumId === 'undefined') {
        console.error('当前相册ID无效:', currentAlbumId);
        showToast('请先选择一个相册', 'error');
        // 跳转到相册列表页面
        setTimeout(() => {
            showPage('albums');
        }, 1500);
        return;
    }

    const uploadArea = document.getElementById('upload-area');
    const uploadContent = uploadArea.querySelector('.upload-content');
    const uploadProgress = document.getElementById('upload-progress');
    const progressFill = document.getElementById('progress-fill');
    const progressText = document.getElementById('progress-text');

    // 验证文件
    const validFiles = [];
    for (const file of files) {
        if (file.type.startsWith('image/') || file.type.startsWith('video/')) {
            if (file.size <= 500 * 1024 * 1024) { // 500MB限制
                validFiles.push(file);
            } else {
                showToast(`文件 ${file.name} 太大，请选择小于500MB的文件`, 'error');
            }
        } else {
            showToast(`文件 ${file.name} 格式不支持`, 'error');
        }
    }

    if (validFiles.length === 0) return;

    // 显示进度
    uploadContent.style.display = 'none';
    uploadProgress.style.display = 'block';

    try {
        for (let i = 0; i < validFiles.length; i++) {
            const file = validFiles[i];
            const baseProgress = (i / validFiles.length) * 100;

            progressText.textContent = `处理文件 ${file.name}... (${i + 1}/${validFiles.length})`;

            // 根据文件大小选择上传方式
            if (file.size > 50 * 1024 * 1024) { // 大于50MB使用分块上传
                await uploadLargeFile(file, currentAlbumId, (chunkProgress) => {
                    const totalProgress = baseProgress + (chunkProgress / validFiles.length);
                    progressFill.style.width = `${totalProgress}%`;
                    progressText.textContent = `上传 ${file.name}... ${Math.round(chunkProgress)}% (${i + 1}/${validFiles.length})`;
                });
            } else {
                // 小文件直接上传
                await uploadSmallFile(file, currentAlbumId);
                const progress = ((i + 1) / validFiles.length) * 100;
                progressFill.style.width = `${progress}%`;
                progressText.textContent = `上传完成 ${file.name} (${i + 1}/${validFiles.length})`;
            }
        }

        showToast('上传成功', 'success');
        loadAlbumDetail(currentAlbumId); // 重新加载媒体

    } catch (error) {
        console.error('上传错误:', error);
        showToast(error.message || '上传失败', 'error');
    } finally {
        // 隐藏进度，显示上传区域
        uploadProgress.style.display = 'none';
        uploadContent.style.display = 'block';
        progressFill.style.width = '0%';

        // 清空文件选择
        const mediaUpload = document.getElementById('media-upload');
        if (mediaUpload) mediaUpload.value = '';
    }
}

// 小文件直接上传（小于50MB）
async function uploadSmallFile(file, albumId) {
    try {
        // 转换为Base64
        const base64Data = await fileToBase64(file);

        // 上传文件
        const response = await fetch(`/api/albums/${albumId}/media`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            credentials: 'include',
            body: JSON.stringify({
                media_type: file.type.startsWith('image/') ? 'image' : 'video',
                media_data: base64Data,
                file_name: file.name,
                file_size: file.size
            })
        });

        if (!response.ok) {
            throw new Error(`上传 ${file.name} 失败`);
        }

        return await response.json();
    } catch (error) {
        console.error('小文件上传错误:', error);
        throw error;
    }
}

// 大文件分块上传（大于50MB）
async function uploadLargeFile(file, albumId, progressCallback) {
    const CHUNK_SIZE = 10 * 1024 * 1024; // 10MB per chunk
    const totalChunks = Math.ceil(file.size / CHUNK_SIZE);
    const uploadId = `upload_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    try {
        // 初始化分块上传
        const initResponse = await fetch(`/api/albums/${albumId}/media/init-upload`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            credentials: 'include',
            body: JSON.stringify({
                upload_id: uploadId,
                file_name: file.name,
                file_size: file.size,
                media_type: file.type.startsWith('image/') ? 'image' : 'video',
                total_chunks: totalChunks
            })
        });

        if (!initResponse.ok) {
            throw new Error('初始化分块上传失败');
        }

        // 逐个上传分块
        for (let chunkIndex = 0; chunkIndex < totalChunks; chunkIndex++) {
            const start = chunkIndex * CHUNK_SIZE;
            const end = Math.min(start + CHUNK_SIZE, file.size);
            const chunk = file.slice(start, end);
            
            // 转换分块为Base64
            const chunkBase64 = await fileToBase64(chunk);
            
            // 上传分块
            const chunkResponse = await fetch(`/api/albums/${albumId}/media/upload-chunk`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                credentials: 'include',
                body: JSON.stringify({
                    upload_id: uploadId,
                    chunk_index: chunkIndex,
                    chunk_data: chunkBase64,
                    is_last_chunk: chunkIndex === totalChunks - 1
                })
            });

            if (!chunkResponse.ok) {
                throw new Error(`上传分块 ${chunkIndex + 1} 失败`);
            }

            // 更新进度
            const progress = ((chunkIndex + 1) / totalChunks) * 100;
            if (progressCallback) {
                progressCallback(progress);
            }

            // 释放内存 - 注意：chunk和chunkBase64是局部变量，会在循环结束时自动释放
            // 这里我们不需要手动设置为null，因为它们会在作用域结束时被垃圾回收
            
            // 强制垃圾回收（如果浏览器支持）
            if (window.gc) {
                window.gc();
            }
        }

        // 完成上传
        const completeResponse = await fetch(`/api/albums/${albumId}/media/complete-upload`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            credentials: 'include',
            body: JSON.stringify({
                upload_id: uploadId
            })
        });

        if (!completeResponse.ok) {
            throw new Error('完成分块上传失败');
        }

        return await completeResponse.json();
    } catch (error) {
        console.error('大文件上传错误:', error);
        // 清理失败的上传
        try {
            await fetch(`/api/albums/${albumId}/media/cleanup-upload`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                credentials: 'include',
                body: JSON.stringify({
                    upload_id: uploadId
                })
            });
        } catch (cleanupError) {
            console.error('清理上传失败:', cleanupError);
        }
        throw error;
    }
}

// 文件转Base64（优化内存使用）
function fileToBase64(file) {
    return new Promise((resolve, reject) => {
        const reader = new FileReader();
        reader.onload = () => {
            const result = reader.result;
            // 清理reader引用
            reader.onload = null;
            reader.onerror = null;
            resolve(result);
        };
        reader.onerror = (error) => {
            reader.onload = null;
            reader.onerror = null;
            reject(error);
        };
        reader.readAsDataURL(file);
    });
}

// 显示相册媒体预览
function showAlbumMediaPreview(index) {
    currentMediaIndex = index;
    const modal = document.getElementById('media-preview-modal');
    const mediaContainer = document.getElementById('media-container');
    const mediaCounter = document.getElementById('media-counter');

    if (!modal || !mediaContainer || !mediaCounter) return;

    updateMediaPreview();

    modal.style.display = 'flex';
    setTimeout(() => modal.classList.add('active'), 10);
}

// 更新媒体预览
function updateMediaPreview() {
    const mediaContainer = document.getElementById('media-container');
    const mediaCounter = document.getElementById('media-counter');
    const prevBtn = document.querySelector('.prev-btn');
    const nextBtn = document.querySelector('.next-btn');

    if (!mediaContainer || !mediaCounter) return;

    const media = currentAlbumMedia[currentMediaIndex];
    if (!media) return;

    // 更新媒体内容
    if (media.media_type === 'video') {
        mediaContainer.innerHTML = `<video src="${media.media_url}" controls autoplay>`;
    } else {
        mediaContainer.innerHTML = `<img src="${media.media_url}" alt="照片">`;
    }

    // 更新计数器
    mediaCounter.textContent = `${currentMediaIndex + 1} / ${currentAlbumMedia.length}`;

    // 更新导航按钮状态
    if (prevBtn) prevBtn.disabled = currentMediaIndex === 0;
    if (nextBtn) nextBtn.disabled = currentMediaIndex === currentAlbumMedia.length - 1;
}

// 隐藏媒体预览
function hideMediaPreview() {
    const modal = document.getElementById('media-preview-modal');
    if (modal) {
        modal.classList.remove('active');
        setTimeout(() => {
            modal.style.display = 'none';
            // 停止视频播放
            const video = modal.querySelector('video');
            if (video) {
                video.pause();
                video.currentTime = 0;
            }
        }, 300);
    }
}

// 显示上一个媒体
function showPrevMedia() {
    if (currentMediaIndex > 0) {
        currentMediaIndex--;
        updateMediaPreview();
    }
}

// 显示下一个媒体
function showNextMedia() {
    if (currentMediaIndex < currentAlbumMedia.length - 1) {
        currentMediaIndex++;
        updateMediaPreview();
    }
}

// 删除当前相册
function deleteCurrentAlbum() {
    console.log('=== deleteCurrentAlbum 被调用 ===');
    console.log('currentAlbumId:', currentAlbumId);

    if (!currentAlbumId) {
        console.log('currentAlbumId 为空，退出');
        showToast('相册ID无效', 'error');
        return;
    }

    deletingAlbumId = currentAlbumId;

    // 弹窗元素现在在主页面中，应该总是可用
    const modal = document.getElementById('delete-confirm-modal');
    console.log('删除确认弹窗元素:', !!modal);

    if (!modal) {
        console.error('删除确认弹窗元素未找到');
        showToast('删除确认界面加载失败', 'error');
        return;
    }

    console.log('显示删除确认弹窗...');
    modal.style.display = 'flex';
    // 强制重排
    modal.offsetHeight;
    setTimeout(() => {
        modal.classList.add('active');
        console.log('✅ 删除确认弹窗显示成功');
    }, 10);
}

// 隐藏删除确认弹窗
function hideDeleteConfirmModal() {
    const modal = document.getElementById('delete-confirm-modal');
    if (modal) {
        modal.classList.remove('active');
        setTimeout(() => {
            modal.style.display = 'none';
        }, 300);
    }
}

// 确认删除相册
async function confirmDeleteAlbum() {
    if (!deletingAlbumId) return;

    try {
        const response = await fetch(`/api/albums/${deletingAlbumId}`, {
            method: 'DELETE',
            credentials: 'include'
        });

        if (response.ok) {
            showToast('相册删除成功', 'success');
            hideDeleteConfirmModal();
            showPage('albums'); // 返回相册列表
        } else {
            const error = await response.json();
            throw new Error(error.message || '删除失败');
        }
    } catch (error) {
        console.error('删除相册错误:', error);
        showToast(error.message || '删除失败', 'error');
    }
}

// 删除当前媒体（预览模式中使用）
function deleteCurrentMedia() {
    const media = currentAlbumMedia[currentMediaIndex];
    if (!media) return;

    deletingMediaId = media.id;
    const modal = document.getElementById('delete-media-confirm-modal');
    if (modal) {
        modal.style.display = 'flex';
        setTimeout(() => modal.classList.add('active'), 10);
    }
}

// 通过媒体ID删除媒体（网格中直接删除）
function deleteMediaById(mediaId) {
    if (!mediaId) return;

    deletingMediaId = mediaId;
    const modal = document.getElementById('delete-media-confirm-modal');
    if (modal) {
        modal.style.display = 'flex';
        setTimeout(() => modal.classList.add('active'), 10);
    }
}

// 隐藏删除媒体确认弹窗
function hideDeleteMediaConfirmModal() {
    const modal = document.getElementById('delete-media-confirm-modal');
    if (modal) {
        modal.classList.remove('active');
        setTimeout(() => {
            modal.style.display = 'none';
        }, 300);
    }
}

// 确认删除媒体
async function confirmDeleteMedia() {
    if (!deletingMediaId || !currentAlbumId) return;

    try {
        const response = await fetch(`/api/albums/${currentAlbumId}/media/${deletingMediaId}`, {
            method: 'DELETE',
            credentials: 'include'
        });

        if (response.ok) {
            showToast('文件删除成功', 'success');
            hideDeleteMediaConfirmModal();
            hideMediaPreview();
            loadAlbumDetail(currentAlbumId); // 重新加载媒体
        } else {
            const error = await response.json();
            throw new Error(error.message || '删除失败');
        }
    } catch (error) {
        console.error('删除媒体错误:', error);
        showToast(error.message || '删除失败', 'error');
    }
}

// HTML转义
function escapeHtml(text) {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
}
