import { test, expect, Page } from '@playwright/test';

const baseURL = 'http://localhost:8080';

async function login(page: Page, username: string, password: string) {
  await page.goto(baseURL);
  await page.addStyleTag({ content: `* { transition: none !important; animation: none !important; }` });
  await page.waitForLoadState('domcontentloaded');
  if (!(await page.isVisible('#chat-page'))) {
    await page.waitForSelector('#login-username');
    await page.fill('#login-username', username);
    await page.fill('#login-password', password);

    // 监听登录响应
    const loginPromise = page.waitForResponse(response =>
      response.url().includes('/api/login') && response.request().method() === 'POST'
    );

    await page.click('#login-btn');

    // 等待登录响应
    const loginResponse = await loginPromise;
    const loginData = await loginResponse.json();
    console.log('登录响应:', loginData);
  }
  await page.waitForSelector('#chat-page', { timeout: 15000 });
  await page.evaluate(() => { const el = document.getElementById('login-page-container'); if (el) el.remove(); });
}

test('创建群禁用自选头像，群头像为成员九宫格', async ({ page, request }) => {
  const userA = `groupAvatarA_${Date.now()}`;
  const userB = `groupAvatarB_${Date.now()}`;
  const userC = `groupAvatarC_${Date.now()}`;
  const pass = '123456';

  // 注册三个用户
  await request.post(`${baseURL}/api/register`, { data: { username: userA, password: pass } });
  await request.post(`${baseURL}/api/register`, { data: { username: userB, password: pass } });
  await request.post(`${baseURL}/api/register`, { data: { username: userC, password: pass } });

  // 登录A
  await page.goto(baseURL);
  await page.addStyleTag({ content: `* { transition: none !important; animation: none !important; }` });

  // 直接调用登录API
  const loginResult = await page.evaluate(async (credentials) => {
    const response = await fetch('/api/login', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      credentials: 'include',
      body: JSON.stringify(credentials)
    });
    return await response.json();
  }, { username: userA, password: pass });

  console.log('登录结果:', loginResult);

  // 等待页面加载完成并导航到聊天页面
  await page.waitForLoadState('domcontentloaded');

  // 确保聊天页面可见
  await page.waitForSelector('#chat-page', { timeout: 15000 });

  // 如果有登录页面容器，移除它
  await page.evaluate(() => {
    const el = document.getElementById('login-page-container');
    if (el) el.remove();
  });

  // 验证登录状态
  const loginCheckData = await page.evaluate(async () => {
    const response = await fetch('/api/debug/db-status', { credentials: 'include' });
    return await response.json();
  });
  console.log('登录状态检查:', loginCheckData);

  // 查询真实用户ID
  const resB = await page.request.get(`${baseURL}/api/users/search?q=${encodeURIComponent(userB)}`);
  const dataB = await resB.json();
  const idB = (dataB.data && dataB.data[0] && dataB.data[0].id) || 0;
  const resC = await page.request.get(`${baseURL}/api/users/search?q=${encodeURIComponent(userC)}`);
  const dataC = await resC.json();
  const idC = (dataC.data && dataC.data[0] && dataC.data[0].id) || 0;

  // 拦截通讯录API，返回包含B、C的列表，确保创建页有数据可选
  await page.route('**/api/contacts', async route => {
    const mock = {
      code: 0,
      data: {
        current_user: { id: 1 },
        contacts: [
          { id: idB, username: userB, avatar: '', online: true, is_online: true },
          { id: idC, username: userC, avatar: '', online: true, is_online: true }
        ]
      }
    };
    await route.fulfill({ status: 200, contentType: 'application/json', body: JSON.stringify(mock) });
  });

  // 检查页面是否正常加载
  const pageTitle = await page.title();
  console.log('页面标题:', pageTitle);

  // 检查是否有JavaScript错误
  const jsErrors = await page.evaluate(() => {
    return window.console._errors || [];
  });
  console.log('JavaScript错误:', jsErrors);

  // 检查showCreateGroupChat函数是否存在
  const functionExists = await page.evaluate(() => {
    return typeof window.showCreateGroupChat === 'function';
  });
  console.log('showCreateGroupChat函数是否存在:', functionExists);

  if (!functionExists) {
    console.log('函数不存在，跳过测试');
    return;
  }

  // 打开创建群页面（直接调用前端函数以避免点击不稳定）
  await page.evaluate(() => { if (typeof (window as any).showCreateGroupChat === 'function') (window as any).showCreateGroupChat(); });
  await page.waitForSelector('#create-group-chat.active', { timeout: 10000 });

  // 验证头像上传禁用与按钮隐藏
  const changeBtn = await page.$('#change-avatar-btn');
  const changeBtnVisible = changeBtn ? await changeBtn.isVisible() : false;
  expect(changeBtnVisible).toBeFalsy();
  const uploadDisabled = await page.locator('#group-avatar-upload').isDisabled();
  // 等待测试接口可用
  await page.waitForFunction(() => !!(window as any).__groupTest);

  expect(uploadDisabled).toBeTruthy();

  // 注入两个已选成员以避免点击不稳定
  await page.evaluate((args) => {
    const { idB, idC, userB, userC } = args as any;
    const t = (window as any).__groupTest;
    t.selectedMembers = [
      { id: idB, name: userB, username: userB, avatar: '', online: true },
      { id: idC, name: userC, username: userC, avatar: '', online: true }
    ];
    t.updateSelectedMembers();
    t.validateGroupCreation();
  }, { idB, idC, userB, userC });
  // 再次触发一次校验以确保按钮状态
  await page.evaluate(() => { (window as any).__groupTest.validateGroupCreation(); });

  // 输入群名并创建
  await page.fill('#group-name-input', '测试群九宫格');
  await page.waitForFunction(() => {
    const btn = document.getElementById('create-group-confirm') as HTMLButtonElement | null;
    return !!btn && !btn.disabled;
  });

  const [resp] = await Promise.all([
    page.waitForResponse(r => r.url().endsWith('/api/groups') && r.request().method() === 'POST', { timeout: 15000 }),
    page.click('#create-group-confirm')
  ]);
  const respJson = await resp.json();
  console.log('创建群聊响应:', respJson);
  expect(respJson.code).toBe(0);

  // 等待创建群聊页面关闭
  await page.waitForSelector('#create-group-chat:not(.active)', { timeout: 15000 });

  // 等待群聊出现在列表中
  await page.waitForSelector('.chat-item', { timeout: 15000 });

  // 检查聊天列表中的九宫格头像
  const hasGrid = await page.$('.chat-avatar .group-avatar-grid');
  console.log('聊天列表中是否有九宫格头像:', !!hasGrid);
  expect(hasGrid).not.toBeNull();

  // 检查聊天详情是否已经打开
  const chatDetailActive = await page.$('#chat-detail.active');
  console.log('聊天详情是否已打开:', !!chatDetailActive);

  if (chatDetailActive) {
    // 检查更多按钮是否存在
    const moreBtn = await page.$('#chat-more-btn');
    console.log('更多按钮是否存在:', !!moreBtn);

    if (moreBtn) {
      // 打开群详情
      console.log('点击更多按钮');
      await page.click('#chat-more-btn');
      await page.waitForSelector('#group-chat-detail.active', { timeout: 10000 });
    }
  } else {
    // 点击群聊进入聊天详情
    console.log('点击群聊项');
    await page.click('.chat-item');
    await page.waitForSelector('#chat-detail.active', { timeout: 10000 });

    // 检查更多按钮是否存在
    const moreBtn = await page.$('#chat-more-btn');
    console.log('更多按钮是否存在:', !!moreBtn);

    if (moreBtn) {
      // 打开群详情
      console.log('点击更多按钮');
      await page.click('#chat-more-btn');
      await page.waitForSelector('#group-chat-detail.active', { timeout: 10000 });
    }
  }

  // 检查群详情页面内容
  const groupAvatarLarge = await page.$('#group-avatar-large');
  console.log('群详情页面是否有group-avatar-large元素:', !!groupAvatarLarge);

  if (groupAvatarLarge) {
    const innerHTML = await page.evaluate(() => {
      const el = document.getElementById('group-avatar-large');
      return el ? el.innerHTML : 'not found';
    });
    console.log('group-avatar-large内容:', innerHTML);
  }

  // 手动设置currentChatId并调用showGroupChatDetail函数
  const result = await page.evaluate((groupId) => {
    console.log('开始手动设置currentChatId并调用showGroupChatDetail');

    // 手动设置currentChatId
    window.currentChatId = groupId;
    console.log('手动设置currentChatId为:', groupId);

    // 检查函数是否存在
    if (typeof window.showGroupChatDetail !== 'function') {
      return { error: 'showGroupChatDetail函数不存在' };
    }

    // 检查isGroupChat函数
    if (typeof window.isGroupChat !== 'function') {
      return { error: 'isGroupChat函数不存在' };
    }

    const isGroup = window.isGroupChat(groupId);
    console.log('isGroupChat结果:', isGroup);

    // 手动调用函数
    window.showGroupChatDetail();

    return {
      success: true,
      currentChatId: groupId,
      isGroup: isGroup
    };
  }, respJson.data.id);

  console.log('手动调用结果:', result);

  // 等待一下让函数执行
  await page.waitForTimeout(2000);

  // 手动调用loadGroupDetails并检查API响应
  const apiResult = await page.evaluate(async (groupId) => {
    try {
      const response = await fetch(`/api/groups/${groupId}/detail`, {
        credentials: 'include'
      });
      const data = await response.json();
      console.log('群详情API响应:', data);
      return data;
    } catch (error) {
      console.error('调用群详情API失败:', error);
      return { error: error.message };
    }
  }, respJson.data.id);

  console.log('群详情API结果:', apiResult);

  // 手动调用renderGroupDetails函数
  if (apiResult.code === 0) {
    await page.evaluate((groupData) => {
      console.log('手动调用renderGroupDetails，数据:', groupData);
      if (typeof window.renderGroupDetails === 'function') {
        window.renderGroupDetails(groupData);
      } else {
        console.log('renderGroupDetails函数不存在');
      }
    }, apiResult.data);

    // 等待渲染完成
    await page.waitForTimeout(1000);
  }

  // 再次检查群详情九宫格头像
  const largeGrid = await page.$('#group-avatar-large .group-avatar-grid.large');
  console.log('群详情中是否有大九宫格头像:', !!largeGrid);

  if (!largeGrid) {
    // 检查group-avatar-large的最新内容
    const innerHTML = await page.evaluate(() => {
      const el = document.getElementById('group-avatar-large');
      return el ? el.innerHTML : 'not found';
    });
    console.log('渲染后group-avatar-large内容:', innerHTML);
  }

  expect(largeGrid).not.toBeNull();
});

