* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    font-family: -apple-system, BlinkMacSystemFont, 'Se<PERSON>e UI', Roboto, Helvetica, Arial, sans-serif;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

:root {
    --primary-color: #07c160;
    --primary-hover: #06ae56;
    --primary-light: #20d077;
    --primary-dark: #059649;
    --primary-gradient: linear-gradient(135deg, #07c160 0%, #06ae56 100%);
    --primary-gradient-light: linear-gradient(135deg, #07c160 0%, #20d077 100%);
    --primary-gradient-vibrant: linear-gradient(135deg, #07c160 0%, #06ae56 50%, #20d077 100%);
    --light-primary: #e8f8f0;
    --light-grey: #f8f9fa;
    --medium-grey: #e9ecef;
    --dark-grey: #6c757d;
    --text-primary: #212529;
    --text-secondary: #6c757d;
    --text-muted: #adb5bd;
    --background-gradient: linear-gradient(135deg, #07c160 0%, #06ae56 100%);
    --card-gradient: linear-gradient(135deg, rgba(255, 255, 255, 0.9) 0%, rgba(255, 255, 255, 0.8) 100%);
    --shadow-sm: 0 2px 10px rgba(0, 0, 0, 0.08);
    --shadow-md: 0 8px 25px rgba(0, 0, 0, 0.12);
    --shadow-lg: 0 15px 35px rgba(0, 0, 0, 0.15);
    --shadow-xl: 0 20px 40px rgba(0, 0, 0, 0.2);
    --shadow-primary: 0 8px 32px rgba(7, 193, 96, 0.3);
    --shadow-primary-lg: 0 12px 40px rgba(7, 193, 96, 0.4);
    --transition-fast: 0.2s cubic-bezier(0.4, 0, 0.2, 1);
    --transition-normal: 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    --transition-slow: 0.5s cubic-bezier(0.4, 0, 0.2, 1);
    --border-radius-sm: 8px;
    --border-radius-md: 12px;
    --border-radius-lg: 20px;
    --border-radius-xl: 24px;
    --red: #dc3545;
    --success: #28a745;
    --warning: #ffc107;
    --info: #17a2b8;
    --error-color: #dc3545;
    --warning-color: #ffc107;
}

/* 现代化Toast提示系统 */
.toast-container {
    position: fixed;
    top: 20px;
    left: 50%;
    transform: translateX(-50%);
    z-index: 9999;
    pointer-events: none;
    display: flex;
    flex-direction: column;
    align-items: center;
}

.toast {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border-radius: 16px;
    padding: 16px 24px;
    margin-bottom: 12px;
    box-shadow:
        0 8px 32px rgba(0, 0, 0, 0.12),
        0 4px 16px rgba(0, 0, 0, 0.08),
        0 2px 8px rgba(0, 0, 0, 0.04);
    border: 1px solid rgba(255, 255, 255, 0.6);
    display: flex;
    align-items: center;
    gap: 12px;
    min-width: 300px;
    max-width: 400px;
    transform: translateY(-100px);
    opacity: 0;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    pointer-events: auto;
    position: relative;
    overflow: hidden;
    cursor: pointer;
}

.toast:hover {
    transform: translateY(-5px) scale(1.02);
    box-shadow:
        0 12px 40px rgba(0, 0, 0, 0.15),
        0 6px 20px rgba(0, 0, 0, 0.1),
        0 3px 10px rgba(0, 0, 0, 0.06);
}

.toast.show {
    transform: translateY(0);
    opacity: 1;
}

.toast.hide {
    transform: translateY(-100px);
    opacity: 0;
}

.toast-icon {
    width: 24px;
    height: 24px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 14px;
    flex-shrink: 0;
}

.toast-content {
    flex: 1;
    font-size: 14px;
    font-weight: 500;
    line-height: 1.4;
}

.toast-title {
    font-weight: 600;
    margin-bottom: 2px;
    font-size: 15px;
}

.toast-message {
    color: #666;
    font-size: 13px;
    font-weight: 400;
}

/* Toast类型样式 */
.toast.success {
    border-left: 4px solid var(--success);
}

.toast.success .toast-icon {
    background: linear-gradient(135deg, var(--success) 0%, #20c997 100%);
    color: white;
}

.toast.error {
    border-left: 4px solid var(--red);
    background: rgba(255, 245, 245, 0.98); /* 淡红色背景 */
    border: 1px solid rgba(220, 53, 69, 0.2);
    animation: errorPulse 0.5s ease-in-out;
}

.toast.error .toast-icon {
    background: linear-gradient(135deg, var(--red) 0%, #e74c3c 100%);
    color: white;
    animation: errorIconPulse 2s ease-in-out infinite;
}

.toast.error .toast-content {
    color: #721c24; /* 深红色文字 */
    font-weight: 600;
}

/* 错误Toast动画效果 */
@keyframes errorPulse {
    0% {
        transform: translateY(-120px) scale(0.9);
        opacity: 0;
    }
    50% {
        transform: translateY(-10px) scale(1.02);
        opacity: 0.8;
    }
    100% {
        transform: translateY(0) scale(1);
        opacity: 1;
    }
}

@keyframes errorIconPulse {
    0%, 100% {
        transform: scale(1);
        box-shadow: 0 0 0 0 rgba(220, 53, 69, 0.4);
    }
    50% {
        transform: scale(1.1);
        box-shadow: 0 0 0 8px rgba(220, 53, 69, 0.1);
    }
}

.toast.warning {
    border-left: 4px solid var(--warning);
}

.toast.warning .toast-icon {
    background: linear-gradient(135deg, var(--warning) 0%, #f39c12 100%);
    color: white;
}

.toast.info {
    border-left: 4px solid var(--info);
}

.toast.info .toast-icon {
    background: linear-gradient(135deg, var(--info) 0%, #3498db 100%);
    color: white;
}

/* Toast进度条 */
.toast-progress {
    position: absolute;
    bottom: 0;
    left: 0;
    height: 3px;
    background: linear-gradient(90deg,
        rgba(7, 193, 96, 0.8) 0%,
        rgba(7, 193, 96, 0.4) 100%);
    border-radius: 0 0 16px 16px;
    transform-origin: left;
    animation: toastProgress 3s linear forwards;
}

.toast.error .toast-progress {
    background: linear-gradient(90deg,
        rgba(220, 53, 69, 0.8) 0%,
        rgba(220, 53, 69, 0.4) 100%);
}

.toast.warning .toast-progress {
    background: linear-gradient(90deg,
        rgba(255, 193, 7, 0.8) 0%,
        rgba(255, 193, 7, 0.4) 100%);
}

@keyframes toastProgress {
    from {
        transform: scaleX(1);
    }
    to {
        transform: scaleX(0);
    }
}

/* 关闭按钮 */
.toast-close {
    background: rgba(0, 0, 0, 0.05);
    border: none;
    color: #999;
    cursor: pointer;
    padding: 4px;
    border-radius: 50%;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
    flex-shrink: 0;
    font-size: 12px;
}

.toast-close:hover {
    background: rgba(0, 0, 0, 0.15);
    color: #333;
    transform: scale(1.1);
}

.toast.error .toast-close {
    background: rgba(220, 53, 69, 0.1);
    color: #721c24;
}

.toast.error .toast-close:hover {
    background: rgba(220, 53, 69, 0.2);
    color: #721c24;
    transform: scale(1.1);
}

/* 响应式设计 */
@media (max-width: 480px) {
    /* 小屏幕容器优化 */
    .container {
        max-width: none !important;
        margin: 0 !important;
        border: none !important;
        border-radius: 0 !important;
        box-shadow: none !important;
    }

    .toast-container {
        top: 10px;
        left: 50%;
        transform: translateX(-50%);
        width: calc(100% - 20px);
        max-width: 400px;
    }

    .toast {
        min-width: auto;
        max-width: none;
        margin-bottom: 8px;
    }

    /* 小屏幕手机底部导航栏优化 */
    .tab-bar {
        height: 65px;
        max-width: none !important;
        margin: 0 !important;
        border-radius: 0 !important;
        left: 0 !important;
        right: 0 !important;
        width: 100% !important;
    }

    .tab-item {
        margin: 2px 1px;
        padding: 6px 0;
        font-size: 9px;
    }

    .tab-item i {
        font-size: 20px;
        margin-bottom: 2px;
    }
}

html, body {
    margin: 0 !important;
    padding: 0 !important;
}

body {
    background: var(--background-gradient);
    color: var(--text-primary);
    min-height: 100vh;
    position: relative;
}

body::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
        radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.3) 0%, transparent 50%),
        radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.2) 0%, transparent 50%);
    z-index: -1;
    animation: backgroundShift 20s ease-in-out infinite;
}

@keyframes backgroundShift {

    0%,
    100% {
        opacity: 1;
    }

    50% {
        opacity: 0.8;
    }
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(30px);
    }

    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes pulse {
    0% {
        transform: scale(1);
    }

    50% {
        transform: scale(1.05);
    }

    100% {
        transform: scale(1);
    }
}

@keyframes shimmer {
    0% {
        background-position: -200px 0;
    }

    100% {
        background-position: calc(200px + 100%) 0;
    }
}

.fade-in-up {
    animation: fadeInUp 0.6s ease-out;
}

.slide-in-right {
    animation: slideInRight 0.5s ease-out;
}

.pulse-animation {
    animation: pulse 2s infinite;
}

.container {
    max-width: 600px;
    margin: 0 auto;
    background: var(--card-gradient);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    min-height: 100vh;
    position: relative;
    overflow: hidden;
    box-shadow: var(--shadow-xl);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

/* 主内容区域 */
.main-content {
    height: calc(100vh - 70px);
    overflow-y: auto;
    overflow-x: hidden; /* 隐藏水平滚动条 */
    position: relative;
    scroll-behavior: smooth;
    -webkit-overflow-scrolling: touch;
}

/* 自定义滚动条样式 */
.main-content::-webkit-scrollbar {
    width: 6px;
}

.main-content::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 10px;
}

.main-content::-webkit-scrollbar-thumb {
    background: rgba(7, 193, 96, 0.3);
    border-radius: 10px;
    transition: all var(--transition-fast);
}

.main-content::-webkit-scrollbar-thumb:hover {
    background: rgba(7, 193, 96, 0.5);
}

/* 页面通用样式 */
.page {
    display: none;
    height: 100%;
    overflow-y: auto;
    overflow-x: hidden; /* 隐藏水平滚动条 */
    opacity: 0;
    transition: opacity var(--transition-normal);
}

.page.active {
    display: block;
    opacity: 1;
}

.page-header {
    padding: 20px 15px;
    text-align: center;
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    position: sticky;
    top: 0;
    z-index: 10;
    box-shadow: var(--shadow-sm);
    border-radius: 0 0 var(--border-radius-md) var(--border-radius-md);
}

.page-header h2 {
    font-size: 20px;
    font-weight: 700;
    background: var(--primary-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    letter-spacing: 0.5px;
}

/* 底部导航栏 */
.tab-bar {
    display: flex;
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    max-width: 600px;
    margin: 0 auto;
    height: 70px;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border-top: 1px solid rgba(255, 255, 255, 0.2);
    z-index: 100;
    box-shadow: var(--shadow-md);
    transition: transform var(--transition-normal);
    border-radius: var(--border-radius-md) var(--border-radius-md) 0 0;
}

.tab-item {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    color: var(--text-secondary);
    font-size: 11px;
    font-weight: 500;
    cursor: pointer;
    position: relative;
    transition: all var(--transition-fast);
    padding: 10px 0;
    border-radius: var(--border-radius-sm);
    margin: 5px;
}

.tab-item:hover {
    background: rgba(7, 193, 96, 0.05);
    transform: translateY(-2px);
}

.tab-item i {
    font-size: 24px;
    margin-bottom: 4px;
    transition: all var(--transition-fast);
    position: relative;
}

.tab-item.active {
    color: var(--primary-color);
    background: rgba(7, 193, 96, 0.1);
}

.tab-item.active i {
    transform: scale(1.15);
    filter: drop-shadow(0 2px 4px rgba(7, 193, 96, 0.3));
}

.tab-item::before {
    content: '';
    position: absolute;
    top: 8px;
    left: 50%;
    width: 0;
    height: 0;
    background: var(--primary-gradient);
    border-radius: 50%;
    transition: all var(--transition-normal);
    transform: translateX(-50%);
    z-index: -1;
}

.tab-item.active::before {
    width: 40px;
    height: 40px;
    opacity: 0.15;
}

/* 底部导航栏图标容器 */
.tab-icon-container {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* 底部导航栏徽标 */
.tab-badge {
    position: absolute;
    top: -8px;
    right: -12px;
    background: #ff3b30;
    color: white;
    font-size: 10px;
    font-weight: 600;
    min-width: 18px;
    height: 18px;
    border-radius: 9px;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0 4px;
    box-shadow: 0 2px 6px rgba(255, 59, 48, 0.4);
    border: 2px solid white;
    z-index: 10;
    animation: badgePulse 2s infinite;
    transform-origin: center;
}

.tab-badge:empty {
    display: none !important;
}

@keyframes badgePulse {
    0% {
        transform: scale(1);
    }
    10% {
        transform: scale(1.1);
    }
    20% {
        transform: scale(1);
    }
    100% {
        transform: scale(1);
    }
}

/* 表情选择器 - 通用样式 */
.emoji-picker {
    display: none;
    background: rgba(255, 255, 255, 0.98);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-xl);
    padding: 20px;
    margin-top: 12px;
    z-index: 5;
    opacity: 0;
    transition: opacity var(--transition-normal);
}

.emoji-picker.active {
    display: block;
    opacity: 1;
}

.emoji-container {
    display: grid;
    grid-template-columns: repeat(8, 1fr);
    gap: 8px;
    max-height: 200px;
    overflow-y: auto;
    padding: 5px;
}

.emoji {
    font-size: 28px;
    text-align: center;
    cursor: pointer;
    transition: all var(--transition-fast);
    user-select: none;
    padding: 8px;
    border-radius: var(--border-radius-sm);
    background: rgba(255, 255, 255, 0.5);
}

.emoji:hover {
    transform: scale(1.4);
    background: rgba(7, 193, 96, 0.1);
    box-shadow: var(--shadow-sm);
}

.emoji-btn {
    width: 44px;
    height: 44px;
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 20px;
    color: var(--text-secondary);
    cursor: pointer;
    transition: all var(--transition-fast);
    box-shadow: var(--shadow-sm);
    border: 1px solid rgba(255, 255, 255, 0.3);
}

.emoji-btn:hover {
    background: rgba(7, 193, 96, 0.1);
    color: var(--primary-color);
    transform: translateY(-2px) scale(1.05);
    box-shadow: var(--shadow-md);
}

/* 媒体查看器弹窗样式 */
.media-viewer-modal {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
        radial-gradient(circle at center, rgba(0, 0, 0, 0.8) 0%, rgba(0, 0, 0, 0.95) 100%);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    display: none;
    justify-content: center;
    align-items: center;
    z-index: 300;
    opacity: 0;
    transition: opacity var(--transition-normal);
}

.media-viewer-modal.active {
    opacity: 1;
}

.media-viewer-container {
    position: relative;
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
}

.close-media-btn {
    position: absolute;
    top: 20px;
    right: 20px;
    width: 40px;
    height: 40px;
    background-color: rgba(255, 255, 255, 0.2);
    color: #fff;
    border: none;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 20px;
    cursor: pointer;
    z-index: 2;
    backdrop-filter: blur(5px);
    -webkit-backdrop-filter: blur(5px);
    transition: all var(--transition-fast);
}

.close-media-btn:hover {
    background-color: rgba(255, 255, 255, 0.4);
    transform: scale(1.05);
}

/* 图片查看器样式 */
.image-viewer-content {
    position: relative;
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
}

.image-container {
    max-width: 100%;
    max-height: 90vh;
    position: relative;
    display: flex;
    justify-content: center;
    align-items: center;
    transform: scale(0.95);
    opacity: 0;
    transition: all 0.4s ease;
}

.image-container img {
    max-width: 100%;
    max-height: 90vh;
    object-fit: contain;
    border-radius: var(--border-radius-sm);
    box-shadow: var(--shadow-lg);
}

.image-navigation {
    position: absolute;
    bottom: 20px;
    left: 0;
    right: 0;
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 20px;
}

.nav-btn {
    width: 44px;
    height: 44px;
    background-color: rgba(255, 255, 255, 0.2);
    color: #fff;
    border: none;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 18px;
    cursor: pointer;
    backdrop-filter: blur(5px);
    -webkit-backdrop-filter: blur(5px);
    transition: all var(--transition-fast);
}

.nav-btn:hover {
    background-color: rgba(255, 255, 255, 0.4);
    transform: scale(1.05);
}

.image-counter {
    color: #fff;
    font-size: 16px;
    min-width: 50px;
    text-align: center;
    background-color: rgba(0, 0, 0, 0.5);
    padding: 5px 10px;
    border-radius: 20px;
    backdrop-filter: blur(5px);
    -webkit-backdrop-filter: blur(5px);
}

/* 视频查看器样式 */
.video-viewer-content {
    max-width: 100%;
    max-height: 100vh;
    display: flex;
    justify-content: center;
    align-items: center;
}

#video-player {
    max-width: 100%;
    max-height: 90vh;
    width: auto;
    height: auto;
    border-radius: var(--border-radius-sm);
    box-shadow: var(--shadow-lg);
}

/* 额外的动
画效果 */
@keyframes bounce {

    0%,
    20%,
    53%,
    80%,
    100% {
        transform: translate3d(0, 0, 0);
    }

    40%,
    43% {
        transform: translate3d(0, -8px, 0);
    }

    70% {
        transform: translate3d(0, -4px, 0);
    }

    90% {
        transform: translate3d(0, -2px, 0);
    }
}

@keyframes typing {
    0% {
        width: 0;
    }

    100% {
        width: 100%;
    }
}

@keyframes blink {

    0%,
    50% {
        opacity: 1;
    }

    51%,
    100% {
        opacity: 0;
    }
}

@keyframes float {

    0%,
    100% {
        transform: translateY(0px);
    }

    50% {
        transform: translateY(-10px);
    }
}

@keyframes glow {

    0%,
    100% {
        box-shadow: 0 0 5px rgba(7, 193, 96, 0.3);
    }

    50% {
        box-shadow: 0 0 20px rgba(7, 193, 96, 0.6), 0 0 30px rgba(7, 193, 96, 0.4);
    }
}

@keyframes heartbeat {
    0% {
        transform: scale(1);
    }

    14% {
        transform: scale(1.3);
    }

    28% {
        transform: scale(1);
    }

    42% {
        transform: scale(1.3);
    }

    70% {
        transform: scale(1);
    }
}

.bounce-animation {
    animation: bounce 1s ease-in-out;
}

.float-animation {
    animation: float 3s ease-in-out infinite;
}

.glow-animation {
    animation: glow 2s ease-in-out infinite;
}

.heartbeat-animation {
    animation: heartbeat 1.5s ease-in-out infinite;
}

/* 加载骨架屏 */
.skeleton {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: shimmer 1.5s infinite;
    border-radius: var(--border-radius-sm);
}

.skeleton-text {
    height: 16px;
    margin-bottom: 8px;
}

.skeleton-text.short {
    width: 60%;
}

.skeleton-text.long {
    width: 90%;
}

.skeleton-avatar {
    width: 50px;
    height: 50px;
    border-radius: 50%;
}

.skeleton-image {
    width: 100%;
    height: 200px;
}

/* 打字效果 */
.typing-effect {
    overflow: hidden;
    border-right: 2px solid var(--primary-color);
    white-space: nowrap;
    animation: typing 2s steps(40, end), blink 1s infinite;
}

/* 消息状态指示器 */
.message-status {
    display: inline-flex;
    align-items: center;
    margin-left: 8px;
    font-size: 12px;
    color: var(--text-muted);
}

.message-status.sending {
    color: var(--warning);
}

.message-status.sent {
    color: var(--success);
}

.message-status.read {
    color: var(--success);
}

/* 已读状态的双勾勾样式 */
.message-status.read i:first-child {
    margin-right: -3px;
    opacity: 0.8;
}

.message-status.read i:last-child {
    margin-left: -3px;
}

.message-status.failed {
    color: var(--red);
    cursor: pointer;
}

/* 在线状态指示器 */
.online-indicator {
    position: absolute;
    bottom: 2px;
    right: 2px;
    width: 12px;
    height: 12px;
    background-color: var(--success);
    border: 2px solid #fff;
    border-radius: 50%;
    animation: pulse 2s infinite;
}

.online-indicator.offline {
    background-color: var(--dark-grey);
    animation: none;
}

/* 通知徽章 */
.notification-badge {
    position: absolute;
    top: -5px;
    right: -5px;
    min-width: 18px;
    height: 18px;
    background: var(--red);
    color: white;
    border-radius: 9px;
    font-size: 11px;
    font-weight: 600;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0 4px;
    animation: bounce 0.5s ease-in-out;
    box-shadow: 0 2px 4px rgba(220, 53, 69, 0.3);
}

/* 加载状态 */
.loading-spinner {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 2px solid rgba(7, 193, 96, 0.3);
    border-radius: 50%;
    border-top-color: var(--primary-color);
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}

/* 移除重复的简单Toast样式，统一使用现代化Toast系统 */

/ * 搜索功能样式 */ .search-dialog {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(5px);
    -webkit-backdrop-filter: blur(5px);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

.search-container {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border-radius: var(--border-radius-lg);
    padding: 24px;
    min-width: 400px;
    max-width: 500px;
    box-shadow: var(--shadow-xl);
    border: 1px solid rgba(255, 255, 255, 0.3);
}

.search-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.search-header h3 {
    font-size: 18px;
    font-weight: 600;
    color: var(--text-primary);
}

.close-search-btn {
    background: none;
    border: none;
    font-size: 18px;
    color: var(--dark-grey);
    cursor: pointer;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: all var(--transition-fast);
}

.close-search-btn:hover {
    background-color: rgba(0, 0, 0, 0.1);
    color: var(--text-primary);
}

.search-input-container {
    display: flex;
    align-items: center;
    gap: 12px;
}

#message-search-input {
    flex: 1;
    padding: 12px 16px;
    border: 1px solid var(--medium-grey);
    border-radius: var(--border-radius-md);
    font-size: 16px;
    background: rgba(255, 255, 255, 0.9);
    transition: all var(--transition-fast);
}

#message-search-input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(7, 193, 96, 0.1);
}

.search-controls {
    display: flex;
    align-items: center;
    gap: 8px;
}

.search-nav-btn {
    width: 32px;
    height: 32px;
    border: 1px solid var(--medium-grey);
    background: rgba(255, 255, 255, 0.9);
    border-radius: var(--border-radius-sm);
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all var(--transition-fast);
}

.search-nav-btn:hover:not(:disabled) {
    background-color: var(--light-primary);
    border-color: var(--primary-color);
    color: var(--primary-color);
}

.search-nav-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

#search-counter {
    font-size: 14px;
    color: var(--text-secondary);
    min-width: 40px;
    text-align: center;
}

.search-highlight {
    background-color: rgba(7, 193, 96, 0.1) !important;
    animation: searchHighlight 2s ease-in-out;
}

@keyframes searchHighlight {

    0%,
    100% {
        background-color: rgba(7, 193, 96, 0.1);
    }

    50% {
        background-color: rgba(7, 193, 96, 0.3);
    }
}

/* 搜索结果高亮 */
.message-bubble mark {
    background-color: #ffeb3b;
    color: #333;
    padding: 2px 4px;
    border-radius: 3px;
    font-weight: 600;
}

/* 聊天统计样式 */
.chat-stats-modal {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(5px);
    -webkit-backdrop-filter: blur(5px);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

.stats-container {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border-radius: var(--border-radius-lg);
    padding: 24px;
    min-width: 350px;
    max-width: 450px;
    box-shadow: var(--shadow-xl);
    border: 1px solid rgba(255, 255, 255, 0.3);
}

.stats-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
    padding-bottom: 16px;
    border-bottom: 1px solid var(--medium-grey);
}

.stats-header h3 {
    font-size: 18px;
    font-weight: 600;
    color: var(--text-primary);
}

.close-stats-btn {
    background: none;
    border: none;
    font-size: 18px;
    color: var(--dark-grey);
    cursor: pointer;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: all var(--transition-fast);
}

.close-stats-btn:hover {
    background-color: rgba(0, 0, 0, 0.1);
    color: var(--text-primary);
}

.stats-content {
    display: grid;
    gap: 16px;
}

.stat-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 16px;
    background: rgba(255, 255, 255, 0.8);
    border-radius: var(--border-radius-md);
    border: 1px solid rgba(255, 255, 255, 0.3);
    transition: all var(--transition-fast);
}

.stat-item:hover {
    background: rgba(7, 193, 96, 0.05);
    border-color: rgba(7, 193, 96, 0.2);
}

.stat-label {
    font-size: 14px;
    color: var(--text-secondary);
    font-weight: 500;
}

.stat-value {
    font-size: 16px;
    color: var(--text-primary);
    font-weight: 600;
}

/* 快捷键提示 */
.keyboard-shortcuts-hint {
    position: fixed;
    bottom: 20px;
    right: 20px;
    background: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 12px 16px;
    border-radius: var(--border-radius-md);
    font-size: 12px;
    z-index: 999;
    opacity: 0;
    transform: translateY(20px);
    transition: all var(--transition-normal);
    pointer-events: none;
}

.keyboard-shortcuts-hint.show {
    opacity: 1;
    transform: translateY(0);
}

.keyboard-shortcuts-hint .shortcut {
    display: block;
    margin-bottom: 4px;
}

.keyboard-shortcuts-hint .shortcut:last-child {
    margin-bottom: 0;
}

.keyboard-shortcuts-hint kbd {
    background: rgba(255, 255, 255, 0.2);
    padding: 2px 6px;
    border-radius: 3px;
    font-family: monospace;
    font-size: 11px;
}

/* 消息状态样式增强 */
.message-status {
    margin-left: 8px;
    font-size: 12px;
    opacity: 0.7;
    transition: all var(--transition-fast);
}

.message-status:hover {
    opacity: 1;
}

.message-status.sending {
    color: var(--warning);
}

.message-status.sent {
    color: var(--success);
}

.message-status.read {
    color: var(--success);
}

/* 已读状态的双勾勾样式 */
.message-status.read i:first-child {
    margin-right: -3px;
    opacity: 0.8;
}

.message-status.read i:last-child {
    margin-left: -3px;
}

.message-status.failed {
    color: var(--red);
    cursor: pointer;
}

.message-status.failed:hover {
    transform: scale(1.1);
}

/* 导出功能按钮样式 */
.export-btn {
    background: var(--primary-gradient);
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: var(--border-radius-md);
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all var(--transition-fast);
    display: flex;
    align-items: center;
    gap: 8px;
}

.export-btn:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.export-btn i {
    font-size: 16px;
}

/* 通知样式增强 */
.notification-popup {
    position: fixed;
    top: 20px;
    right: 20px;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border-radius: var(--border-radius-lg);
    padding: 16px;
    box-shadow: var(--shadow-lg);
    border: 1px solid rgba(255, 255, 255, 0.3);
    z-index: 1001;
    opacity: 0;
    transform: translateX(100%);
    transition: all var(--transition-normal);
    max-width: 300px;
}

.notification-popup.show {
    opacity: 1;
    transform: translateX(0);
}

.notification-popup .notification-header {
    display: flex;
    align-items: center;
    margin-bottom: 8px;
}

.notification-popup .notification-avatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    margin-right: 12px;
    background-size: cover;
    background-position: center;
}

.notification-popup .notification-sender {
    font-weight: 600;
    color: var(--text-primary);
    font-size: 14px;
}

.notification-popup .notification-message {
    color: var(--text-secondary);
    font-size: 13px;
    line-height: 1.4;
}

/* 响应式设计 */
@media (max-width: 600px) {
    /* 移动端容器自适应 */
    .container {
        max-width: none !important;
        margin: 0 !important;
        border: none !important;
        border-radius: 0 !important;
        box-shadow: none !important;
    }

    .search-container,
    .stats-container {
        margin: 20px;
        min-width: auto;
        width: calc(100% - 40px);
    }

    .search-input-container {
        flex-direction: column;
        gap: 16px;
    }

    .search-controls {
        align-self: stretch;
        justify-content: center;
    }

    .keyboard-shortcuts-hint {
        bottom: 80px;
        right: 10px;
        left: 10px;
        text-align: center;
    }

    /* 移动端底部导航栏自适应 */
    .tab-bar {
        max-width: none !important;
        margin: 0 !important;
        left: 0 !important;
        right: 0 !important;
        width: 100% !important;
        border-radius: 0 !important;
    }

    .tab-item {
        margin: 3px 2px;
        padding: 8px 0;
    }

    .tab-item i {
        font-size: 22px;
        margin-bottom: 3px;
    }

    .tab-item {
        font-size: 10px;
    }
}